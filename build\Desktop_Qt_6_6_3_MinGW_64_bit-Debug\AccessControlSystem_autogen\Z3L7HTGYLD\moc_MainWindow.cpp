/****************************************************************************
** Meta object code from reading C++ file 'MainWindow.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.6.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../src/views/MainWindow.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#if __has_include(<QtCore/qtmochelpers.h>)
#include <QtCore/qtmochelpers.h>
#else
QT_BEGIN_MOC_NAMESPACE
#endif


#include <memory>

#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'MainWindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.6.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSAccessControlSCOPEMainWindowENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSAccessControlSCOPEMainWindowENDCLASS = QtMocHelpers::stringData(
    "AccessControl::MainWindow",
    "showMainWindow",
    "",
    "logout",
    "onNavigationItemClicked",
    "QListWidgetItem*",
    "item",
    "onSubTabChanged",
    "index",
    "onHamburgerMenuClicked",
    "showFileMenu",
    "showSettingsMenu",
    "showToolsMenu",
    "showHelpMenu",
    "viewLogs",
    "backupDatabase",
    "exitApplication",
    "fieldReplacement",
    "scheduledTasks",
    "languageSelection",
    "skinSettings",
    "extensionFeatures",
    "operatorManagement",
    "changeCredentials",
    "autoLogin",
    "openAreaManagement",
    "onControllerManualAdd",
    "userManual",
    "softwareUpgrade",
    "feedbackReport",
    "aboutDialog",
    "minimizeWindow",
    "maximizeWindow",
    "closeWindow",
    "updateStatusBar",
    "updateDateTime"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSAccessControlSCOPEMainWindowENDCLASS_t {
    uint offsetsAndSizes[72];
    char stringdata0[26];
    char stringdata1[15];
    char stringdata2[1];
    char stringdata3[7];
    char stringdata4[24];
    char stringdata5[17];
    char stringdata6[5];
    char stringdata7[16];
    char stringdata8[6];
    char stringdata9[23];
    char stringdata10[13];
    char stringdata11[17];
    char stringdata12[14];
    char stringdata13[13];
    char stringdata14[9];
    char stringdata15[15];
    char stringdata16[16];
    char stringdata17[17];
    char stringdata18[15];
    char stringdata19[18];
    char stringdata20[13];
    char stringdata21[18];
    char stringdata22[19];
    char stringdata23[18];
    char stringdata24[10];
    char stringdata25[19];
    char stringdata26[22];
    char stringdata27[11];
    char stringdata28[16];
    char stringdata29[15];
    char stringdata30[12];
    char stringdata31[15];
    char stringdata32[15];
    char stringdata33[12];
    char stringdata34[16];
    char stringdata35[15];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSAccessControlSCOPEMainWindowENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSAccessControlSCOPEMainWindowENDCLASS_t qt_meta_stringdata_CLASSAccessControlSCOPEMainWindowENDCLASS = {
    {
        QT_MOC_LITERAL(0, 25),  // "AccessControl::MainWindow"
        QT_MOC_LITERAL(26, 14),  // "showMainWindow"
        QT_MOC_LITERAL(41, 0),  // ""
        QT_MOC_LITERAL(42, 6),  // "logout"
        QT_MOC_LITERAL(49, 23),  // "onNavigationItemClicked"
        QT_MOC_LITERAL(73, 16),  // "QListWidgetItem*"
        QT_MOC_LITERAL(90, 4),  // "item"
        QT_MOC_LITERAL(95, 15),  // "onSubTabChanged"
        QT_MOC_LITERAL(111, 5),  // "index"
        QT_MOC_LITERAL(117, 22),  // "onHamburgerMenuClicked"
        QT_MOC_LITERAL(140, 12),  // "showFileMenu"
        QT_MOC_LITERAL(153, 16),  // "showSettingsMenu"
        QT_MOC_LITERAL(170, 13),  // "showToolsMenu"
        QT_MOC_LITERAL(184, 12),  // "showHelpMenu"
        QT_MOC_LITERAL(197, 8),  // "viewLogs"
        QT_MOC_LITERAL(206, 14),  // "backupDatabase"
        QT_MOC_LITERAL(221, 15),  // "exitApplication"
        QT_MOC_LITERAL(237, 16),  // "fieldReplacement"
        QT_MOC_LITERAL(254, 14),  // "scheduledTasks"
        QT_MOC_LITERAL(269, 17),  // "languageSelection"
        QT_MOC_LITERAL(287, 12),  // "skinSettings"
        QT_MOC_LITERAL(300, 17),  // "extensionFeatures"
        QT_MOC_LITERAL(318, 18),  // "operatorManagement"
        QT_MOC_LITERAL(337, 17),  // "changeCredentials"
        QT_MOC_LITERAL(355, 9),  // "autoLogin"
        QT_MOC_LITERAL(365, 18),  // "openAreaManagement"
        QT_MOC_LITERAL(384, 21),  // "onControllerManualAdd"
        QT_MOC_LITERAL(406, 10),  // "userManual"
        QT_MOC_LITERAL(417, 15),  // "softwareUpgrade"
        QT_MOC_LITERAL(433, 14),  // "feedbackReport"
        QT_MOC_LITERAL(448, 11),  // "aboutDialog"
        QT_MOC_LITERAL(460, 14),  // "minimizeWindow"
        QT_MOC_LITERAL(475, 14),  // "maximizeWindow"
        QT_MOC_LITERAL(490, 11),  // "closeWindow"
        QT_MOC_LITERAL(502, 15),  // "updateStatusBar"
        QT_MOC_LITERAL(518, 14)   // "updateDateTime"
    },
    "AccessControl::MainWindow",
    "showMainWindow",
    "",
    "logout",
    "onNavigationItemClicked",
    "QListWidgetItem*",
    "item",
    "onSubTabChanged",
    "index",
    "onHamburgerMenuClicked",
    "showFileMenu",
    "showSettingsMenu",
    "showToolsMenu",
    "showHelpMenu",
    "viewLogs",
    "backupDatabase",
    "exitApplication",
    "fieldReplacement",
    "scheduledTasks",
    "languageSelection",
    "skinSettings",
    "extensionFeatures",
    "operatorManagement",
    "changeCredentials",
    "autoLogin",
    "openAreaManagement",
    "onControllerManualAdd",
    "userManual",
    "softwareUpgrade",
    "feedbackReport",
    "aboutDialog",
    "minimizeWindow",
    "maximizeWindow",
    "closeWindow",
    "updateStatusBar",
    "updateDateTime"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSAccessControlSCOPEMainWindowENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       0,    0, // classinfo
      31,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,  200,    2, 0x0a,    1 /* Public */,
       3,    0,  201,    2, 0x0a,    2 /* Public */,
       4,    1,  202,    2, 0x08,    3 /* Private */,
       7,    1,  205,    2, 0x08,    5 /* Private */,
       9,    0,  208,    2, 0x08,    7 /* Private */,
      10,    0,  209,    2, 0x08,    8 /* Private */,
      11,    0,  210,    2, 0x08,    9 /* Private */,
      12,    0,  211,    2, 0x08,   10 /* Private */,
      13,    0,  212,    2, 0x08,   11 /* Private */,
      14,    0,  213,    2, 0x08,   12 /* Private */,
      15,    0,  214,    2, 0x08,   13 /* Private */,
      16,    0,  215,    2, 0x08,   14 /* Private */,
      17,    0,  216,    2, 0x08,   15 /* Private */,
      18,    0,  217,    2, 0x08,   16 /* Private */,
      19,    0,  218,    2, 0x08,   17 /* Private */,
      20,    0,  219,    2, 0x08,   18 /* Private */,
      21,    0,  220,    2, 0x08,   19 /* Private */,
      22,    0,  221,    2, 0x08,   20 /* Private */,
      23,    0,  222,    2, 0x08,   21 /* Private */,
      24,    0,  223,    2, 0x08,   22 /* Private */,
      25,    0,  224,    2, 0x08,   23 /* Private */,
      26,    0,  225,    2, 0x08,   24 /* Private */,
      27,    0,  226,    2, 0x08,   25 /* Private */,
      28,    0,  227,    2, 0x08,   26 /* Private */,
      29,    0,  228,    2, 0x08,   27 /* Private */,
      30,    0,  229,    2, 0x08,   28 /* Private */,
      31,    0,  230,    2, 0x08,   29 /* Private */,
      32,    0,  231,    2, 0x08,   30 /* Private */,
      33,    0,  232,    2, 0x08,   31 /* Private */,
      34,    0,  233,    2, 0x08,   32 /* Private */,
      35,    0,  234,    2, 0x08,   33 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 5,    6,
    QMetaType::Void, QMetaType::Int,    8,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject AccessControl::MainWindow::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_CLASSAccessControlSCOPEMainWindowENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSAccessControlSCOPEMainWindowENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSAccessControlSCOPEMainWindowENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<MainWindow, std::true_type>,
        // method 'showMainWindow'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'logout'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onNavigationItemClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QListWidgetItem *, std::false_type>,
        // method 'onSubTabChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'onHamburgerMenuClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'showFileMenu'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'showSettingsMenu'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'showToolsMenu'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'showHelpMenu'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'viewLogs'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'backupDatabase'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'exitApplication'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'fieldReplacement'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'scheduledTasks'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'languageSelection'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'skinSettings'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'extensionFeatures'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'operatorManagement'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'changeCredentials'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'autoLogin'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'openAreaManagement'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onControllerManualAdd'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'userManual'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'softwareUpgrade'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'feedbackReport'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'aboutDialog'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'minimizeWindow'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'maximizeWindow'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'closeWindow'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'updateStatusBar'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'updateDateTime'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void AccessControl::MainWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<MainWindow *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->showMainWindow(); break;
        case 1: _t->logout(); break;
        case 2: _t->onNavigationItemClicked((*reinterpret_cast< std::add_pointer_t<QListWidgetItem*>>(_a[1]))); break;
        case 3: _t->onSubTabChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 4: _t->onHamburgerMenuClicked(); break;
        case 5: _t->showFileMenu(); break;
        case 6: _t->showSettingsMenu(); break;
        case 7: _t->showToolsMenu(); break;
        case 8: _t->showHelpMenu(); break;
        case 9: _t->viewLogs(); break;
        case 10: _t->backupDatabase(); break;
        case 11: _t->exitApplication(); break;
        case 12: _t->fieldReplacement(); break;
        case 13: _t->scheduledTasks(); break;
        case 14: _t->languageSelection(); break;
        case 15: _t->skinSettings(); break;
        case 16: _t->extensionFeatures(); break;
        case 17: _t->operatorManagement(); break;
        case 18: _t->changeCredentials(); break;
        case 19: _t->autoLogin(); break;
        case 20: _t->openAreaManagement(); break;
        case 21: _t->onControllerManualAdd(); break;
        case 22: _t->userManual(); break;
        case 23: _t->softwareUpgrade(); break;
        case 24: _t->feedbackReport(); break;
        case 25: _t->aboutDialog(); break;
        case 26: _t->minimizeWindow(); break;
        case 27: _t->maximizeWindow(); break;
        case 28: _t->closeWindow(); break;
        case 29: _t->updateStatusBar(); break;
        case 30: _t->updateDateTime(); break;
        default: ;
        }
    }
}

const QMetaObject *AccessControl::MainWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AccessControl::MainWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSAccessControlSCOPEMainWindowENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int AccessControl::MainWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 31)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 31;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 31)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 31;
    }
    return _id;
}
QT_WARNING_POP
