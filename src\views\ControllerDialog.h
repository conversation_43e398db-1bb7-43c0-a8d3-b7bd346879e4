#ifndef CONTROLLERDIALOG_H
#define CONTROLLERDIALOG_H

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QFormLayout>
#include <QTabWidget>
#include <QGroupBox>
#include <QLabel>
#include <QLineEdit>
#include <QSpinBox>
#include <QComboBox>
#include <QCheckBox>
#include <QRadioButton>
#include <QButtonGroup>
#include <QTextEdit>
#include <QPushButton>
#include <QMessageBox>
#include <QTimer>
#include <memory>

#include "../models/Controller.h"
#include "../database/dao/ControllerDao.h"
#include "../database/dao/AreaDao.h"
#include "../database/IDatabaseProvider.h"

namespace AccessControl {

/**
 * @brief 控制器添加/编辑对话框
 * 非模态对话框，用于添加或编辑门禁控制器
 */
class ControllerDialog : public QDialog
{
    Q_OBJECT

public:
    /**
     * @brief 对话框模式枚举
     */
    enum class Mode {
        Add,    // 添加模式
        Edit    // 编辑模式
    };

    /**
     * @brief 构造函数
     * @param dbProvider 数据库提供者
     * @param parent 父窗口
     * @param mode 对话框模式
     * @param controllerId 控制器ID（编辑模式时使用）
     */
    explicit ControllerDialog(std::shared_ptr<IDatabaseProvider> dbProvider,
                             QWidget *parent = nullptr,
                             Mode mode = Mode::Add,
                             int controllerId = -1);

    ~ControllerDialog();

private slots:
    /**
     * @brief 序列号输入变化处理
     */
    void onSerialNumberChanged();

    /**
     * @brief 网络模式变化处理
     */
    void onNetworkModeChanged();

    /**
     * @brief 保存按钮点击处理
     */
    void onSaveClicked();

    /**
     * @brief 取消按钮点击处理
     */
    void onCancelClicked();

    /**
     * @brief 控制器编号输入变化处理
     */
    void onControllerNumberChanged();

    /**
     * @brief 区域选择变化处理
     */
    void onAreaChanged();

private:
    // ========== 初始化方法 ==========
    void initializeUI();
    void initializeBasicInfoTab();
    void initializeNetworkConfig(QFormLayout* parentLayout);
    void initializeDoorConfig(QVBoxLayout* parentLayout);
    void initializeButtonBox();
    void initializeConnections();
    void initializeStyles();

    // ========== 数据处理方法 ==========
    void loadControllerData();
    void loadAreaData();
    Controller getControllerFromForm();
    void setControllerToForm(const Controller& controller);
    void updateDoorConfigUI();
    void updateSerialNumberInfo();
    void updateNetworkConfigUI();

    // ========== 验证方法 ==========
    QPair<bool, QString> validateForm();
    bool validateBasicInfo();
    bool validateNetworkConfig();
    bool validateDoorConfig();

    // ========== 工具方法 ==========
    void setFormEnabled(bool enabled);
    void resetForm();
    QString getControllerTypeText(int type);
    QString getSeriesNameText(const QString& serialNumber);

private:
    // ========== 成员变量 ==========
    std::shared_ptr<IDatabaseProvider> m_databaseProvider;
    std::unique_ptr<ControllerDao> m_controllerDao;
    std::unique_ptr<AreaDao> m_areaDao;

    Mode m_mode;
    int m_controllerId;
    Controller m_currentController;

    // ========== UI组件 ==========
    QVBoxLayout* m_mainLayout;

    // 基本信息页面
    QWidget* m_basicInfoTab;
    QFormLayout* m_basicInfoLayout;
    QSpinBox* m_controllerNumberSpinBox;
    QLineEdit* m_serialNumberEdit;
    QLabel* m_controllerTypeLabel;
    QLabel* m_seriesNameLabel;
    QCheckBox* m_enabledCheckBox;
    QComboBox* m_areaComboBox;
    QTextEdit* m_descriptionEdit;
    QCheckBox* m_mobileRemoteCheckBox;
    QCheckBox* m_qrCodeCheckBox;

    // 网络配置组件
    QButtonGroup* m_networkModeGroup;
    QRadioButton* m_lanModeRadio;
    QRadioButton* m_wanModeRadio;
    QGroupBox* m_wanConfigGroup;
    QLineEdit* m_ipAddressEdit;
    QSpinBox* m_portSpinBox;

    // 门配置组件
    QList<QWidget*> m_doorTabs;
    QList<QLineEdit*> m_doorNameEdits;
    QList<QCheckBox*> m_doorEnabledCheckBoxes;
    QList<QComboBox*> m_doorControlModeComboBoxes;
    QList<QSpinBox*> m_doorOpenDelaySpinBoxes;
    QList<QCheckBox*> m_entryReaderEnabledCheckBoxes;
    QList<QCheckBox*> m_exitReaderEnabledCheckBoxes;
    QList<QCheckBox*> m_entryAttendanceCheckBoxes;
    QList<QCheckBox*> m_exitAttendanceCheckBoxes;

    // 按钮区域
    QHBoxLayout* m_buttonLayout;
    QPushButton* m_saveButton;
    QPushButton* m_cancelButton;

    // 定时器
    QTimer* m_validationTimer;
};

} // namespace AccessControl

#endif // CONTROLLERDIALOG_H
