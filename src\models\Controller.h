#ifndef CONTROLLER_H
#define CONTROLLER_H

#include <QString>
#include <QDateTime>
#include <QVariant>

namespace AccessControl {

/**
 * @brief 门禁控制器模型类
 * 管理门禁控制器的基本信息、网络配置和门配置
 */
class Controller {
public:
    /**
     * @brief 网络连接模式枚举
     */
    enum class NetworkMode {
        LAN = 0,        // 小型局域网[同网段]
        WAN = 1         // 中大型局域网[跨网段]或Internet互联网
    };

    /**
     * @brief 门控制方式枚举
     */
    enum class DoorControlMode {
        Online = 0,     // 在线
        AlwaysOpen = 1, // 常开
        AlwaysClosed = 2 // 常闭
    };

    /**
     * @brief 读卡器性质枚举
     */
    enum class ReaderType {
        Entry = 0,      // 进门
        Exit = 1        // 出门
    };

    /**
     * @brief 门配置结构体
     */
    struct DoorConfig {
        QString name;                           // 门名称
        bool enabled = true;                    // 是否启用
        DoorControlMode controlMode = DoorControlMode::Online; // 控制方式
        int openDelay = 3;                      // 开门延时(秒)
        
        // 读卡器配置
        bool entryReaderEnabled = true;         // 进门读卡器启用
        bool exitReaderEnabled = true;          // 出门读卡器启用
        bool entryAttendance = false;           // 进门读卡器作考勤
        bool exitAttendance = false;            // 出门读卡器作考勤
        
        DoorConfig() = default;
    };

    /**
     * @brief 构造函数
     */
    Controller();
    Controller(int id, const QString& serialNumber);

    // ========== 基本属性访问器 ==========
    int id() const { return m_id; }
    void setId(int id) { m_id = id; }

    int controllerNumber() const { return m_controllerNumber; }
    void setControllerNumber(int number) { m_controllerNumber = number; }

    QString serialNumber() const { return m_serialNumber; }
    void setSerialNumber(const QString& serialNumber) { m_serialNumber = serialNumber; }

    bool enabled() const { return m_enabled; }
    void setEnabled(bool enabled) { m_enabled = enabled; }

    NetworkMode networkMode() const { return m_networkMode; }
    void setNetworkMode(NetworkMode mode) { m_networkMode = mode; }

    QString ipAddress() const { return m_ipAddress; }
    void setIpAddress(const QString& ip) { m_ipAddress = ip; }

    int port() const { return m_port; }
    void setPort(int port) { m_port = port; }

    int areaId() const { return m_areaId; }
    void setAreaId(int areaId) { m_areaId = areaId; }

    QString description() const { return m_description; }
    void setDescription(const QString& description) { m_description = description; }

    bool mobileRemoteEnabled() const { return m_mobileRemoteEnabled; }
    void setMobileRemoteEnabled(bool enabled) { m_mobileRemoteEnabled = enabled; }

    bool qrCodeEnabled() const { return m_qrCodeEnabled; }
    void setQrCodeEnabled(bool enabled) { m_qrCodeEnabled = enabled; }

    QDateTime createdAt() const { return m_createdAt; }
    void setCreatedAt(const QDateTime& dateTime) { m_createdAt = dateTime; }

    QDateTime updatedAt() const { return m_updatedAt; }
    void setUpdatedAt(const QDateTime& dateTime) { m_updatedAt = dateTime; }

    // ========== 门配置相关 ==========
    DoorConfig doorConfig(int doorIndex) const;
    void setDoorConfig(int doorIndex, const DoorConfig& config);
    
    QList<DoorConfig> allDoorConfigs() const { return m_doorConfigs; }
    void setAllDoorConfigs(const QList<DoorConfig>& configs) { m_doorConfigs = configs; }

    // ========== 业务逻辑方法 ==========
    /**
     * @brief 根据序列号获取控制器类型（门数）
     * @return 1=单门, 2=双门, 4=四门, 0=无效
     */
    int getControllerType() const;

    /**
     * @brief 根据序列号获取系列名称
     * @return 系列名称字符串
     */
    QString getSeriesName() const;

    /**
     * @brief 验证序列号格式是否正确
     * @return true=格式正确, false=格式错误
     */
    bool isValidSerialNumber() const;

    /**
     * @brief 获取最大门数
     */
    int getMaxDoors() const { return getControllerType(); }

    /**
     * @brief 是否为双向控制器（单门和双门控制器）
     */
    bool isBidirectionalController() const;

    /**
     * @brief 验证控制器编号是否有效
     */
    bool isValidControllerNumber() const { return m_controllerNumber > 0; }

    /**
     * @brief 验证IP地址格式
     */
    bool isValidIpAddress() const;

    /**
     * @brief 验证端口号
     */
    bool isValidPort() const { return m_port > 0 && m_port <= 65535; }

    /**
     * @brief 验证描述长度
     */
    bool isValidDescription() const { return m_description.length() <= 60; }

private:
    // 基本信息
    int m_id;
    int m_controllerNumber;         // 控制器编号
    QString m_serialNumber;         // 序列号SN
    bool m_enabled;                 // 是否启用
    
    // 网络配置
    NetworkMode m_networkMode;      // 网络模式
    QString m_ipAddress;            // IP地址
    int m_port;                     // 端口号
    
    // 区域和描述
    int m_areaId;                   // 所在区域ID
    QString m_description;          // 说明
    
    // 扩展功能
    bool m_mobileRemoteEnabled;     // 手机app远程开门
    bool m_qrCodeEnabled;           // 二维码开门功能
    
    // 门配置
    QList<DoorConfig> m_doorConfigs; // 门配置列表
    
    // 时间信息
    QDateTime m_createdAt;          // 创建时间
    QDateTime m_updatedAt;          // 更新时间
};

} // namespace AccessControl

#endif // CONTROLLER_H
