#include "ControllerDialog.h"
#include <QApplication>
#include <QScreen>
#include <QDebug>

namespace AccessControl {

ControllerDialog::ControllerDialog(std::shared_ptr<IDatabaseProvider> dbProvider,
                                 QWidget *parent,
                                 Mode mode,
                                 int controllerId)
    : QDialog(parent)
    , m_databaseProvider(dbProvider)
    , m_controllerDao(std::make_unique<ControllerDao>(dbProvider))
    , m_areaDao(std::make_unique<AreaDao>(dbProvider))
    , m_mode(mode)
    , m_controllerId(controllerId)
    , m_mainLayout(nullptr)
    , m_basicInfoTab(nullptr)
    , m_validationTimer(new QTimer(this))
{
    // 设置对话框属性
    setWindowTitle(mode == Mode::Add ? "添加控制器" : "编辑控制器");
    setWindowFlags(Qt::Window | Qt::WindowCloseButtonHint);
    setModal(false); // 非模态对话框
    resize(800, 600);

    // 居中显示
    if (parent) {
        QRect parentGeometry = parent->geometry();
        int x = parentGeometry.x() + (parentGeometry.width() - width()) / 2;
        int y = parentGeometry.y() + (parentGeometry.height() - height()) / 2;
        move(x, y);
    } else {
        // 在屏幕中央显示
        QScreen* screen = QApplication::primaryScreen();
        QRect screenGeometry = screen->geometry();
        int x = (screenGeometry.width() - width()) / 2;
        int y = (screenGeometry.height() - height()) / 2;
        move(x, y);
    }

    // 初始化UI
    initializeUI();
    initializeConnections();
    initializeStyles();

    // 加载数据
    loadAreaData();

    if (mode == Mode::Edit && controllerId > 0) {
        loadControllerData();
    } else {
        // 添加模式，设置默认值
        m_controllerNumberSpinBox->setValue(m_controllerDao->getNextControllerNumber());
        m_enabledCheckBox->setChecked(true);
        m_mobileRemoteCheckBox->setChecked(true);
        m_qrCodeCheckBox->setChecked(true);
        m_lanModeRadio->setChecked(true);
        m_portSpinBox->setValue(60000);
        onNetworkModeChanged(); // 更新网络配置UI
    }

    // 设置验证定时器
    m_validationTimer->setSingleShot(true);
    m_validationTimer->setInterval(500); // 500ms延迟验证
    connect(m_validationTimer, &QTimer::timeout, this, &ControllerDialog::onSerialNumberChanged);

    qDebug() << "ControllerDialog initialized, mode:" << (mode == Mode::Add ? "Add" : "Edit");
}

ControllerDialog::~ControllerDialog() = default;

void ControllerDialog::initializeUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    m_mainLayout->setSpacing(10);

    // 直接使用基本信息页面，不需要标签页
    initializeBasicInfoTab();

    // 初始化按钮区域
    initializeButtonBox();

    // 添加到主布局
    m_mainLayout->addWidget(m_basicInfoTab, 1);
    m_mainLayout->addLayout(m_buttonLayout);
}

void ControllerDialog::initializeBasicInfoTab()
{
    m_basicInfoTab = new QWidget();

    // 使用水平布局来创建两列
    QHBoxLayout* mainHorizontalLayout = new QHBoxLayout(m_basicInfoTab);
    mainHorizontalLayout->setContentsMargins(10, 10, 10, 10);
    mainHorizontalLayout->setSpacing(20);

    // 左列 - 基本信息和网络配置
    QWidget* leftColumn = new QWidget();
    QFormLayout* leftLayout = new QFormLayout(leftColumn);
    leftLayout->setLabelAlignment(Qt::AlignRight);
    leftLayout->setFormAlignment(Qt::AlignLeft | Qt::AlignTop);
    leftLayout->setVerticalSpacing(10);

    // 控制器编号
    m_controllerNumberSpinBox = new QSpinBox();
    m_controllerNumberSpinBox->setRange(1, 9999);
    m_controllerNumberSpinBox->setObjectName("controllerNumberSpinBox");
    leftLayout->addRow("控制器编号*:", m_controllerNumberSpinBox);

    // 序列号SN - 调整为同一行显示，固定宽度
    QHBoxLayout* serialLayout = new QHBoxLayout();
    m_serialNumberEdit = new QLineEdit();
    m_serialNumberEdit->setMaxLength(9);
    m_serialNumberEdit->setFixedWidth(120); // 固定宽度，适合9位数字
    m_serialNumberEdit->setPlaceholderText("9位数字");
    m_serialNumberEdit->setObjectName("serialNumberEdit");

    m_controllerTypeLabel = new QLabel("未知类型");
    m_controllerTypeLabel->setStyleSheet("color: #666; font-style: italic;");
    m_seriesNameLabel = new QLabel("未知系列");
    m_seriesNameLabel->setStyleSheet("color: #666; font-style: italic;");

    serialLayout->addWidget(m_serialNumberEdit);
    serialLayout->addWidget(new QLabel("类型:"));
    serialLayout->addWidget(m_controllerTypeLabel);
    serialLayout->addWidget(new QLabel("系列:"));
    serialLayout->addWidget(m_seriesNameLabel);
    serialLayout->addStretch(); // 添加弹性空间
    leftLayout->addRow("序列号SN*:", serialLayout);

    // 启用状态 - 紧凑显示
    m_enabledCheckBox = new QCheckBox("启用控制器");
    m_enabledCheckBox->setChecked(true);
    leftLayout->addRow("状态:", m_enabledCheckBox);

    // 所在区域
    m_areaComboBox = new QComboBox();
    m_areaComboBox->setObjectName("areaComboBox");
    leftLayout->addRow("所在区域:", m_areaComboBox);

    // 说明
    m_descriptionEdit = new QTextEdit();
    m_descriptionEdit->setMaximumHeight(60);
    m_descriptionEdit->setPlaceholderText("请输入说明信息（最多60个字符）");
    m_descriptionEdit->setObjectName("descriptionEdit");
    leftLayout->addRow("说明:", m_descriptionEdit);

    // 网络配置 - 在左列
    initializeNetworkConfig(leftLayout);

    // 扩展功能 - 在左列
    QGroupBox* extendedGroup = new QGroupBox("扩展功能");
    QHBoxLayout* extendedLayout = new QHBoxLayout(extendedGroup);

    m_mobileRemoteCheckBox = new QCheckBox("手机app远程开门");
    m_mobileRemoteCheckBox->setChecked(true);
    extendedLayout->addWidget(m_mobileRemoteCheckBox);

    m_qrCodeCheckBox = new QCheckBox("二维码开门功能");
    m_qrCodeCheckBox->setChecked(true);
    extendedLayout->addWidget(m_qrCodeCheckBox);
    extendedLayout->addStretch();

    leftLayout->addRow(extendedGroup);

    // 右列 - 门配置
    QWidget* rightColumn = new QWidget();
    QVBoxLayout* rightLayout = new QVBoxLayout(rightColumn);
    rightLayout->setContentsMargins(0, 0, 0, 0);
    rightLayout->setSpacing(10);

    // 门配置 - 在右列
    initializeDoorConfig(rightLayout);

    // 添加左右两列到主布局
    mainHorizontalLayout->addWidget(leftColumn, 1);
    mainHorizontalLayout->addWidget(rightColumn, 1);
}

void ControllerDialog::initializeNetworkConfig(QFormLayout* parentLayout)
{
    // 网络配置组
    QGroupBox* networkGroup = new QGroupBox("网络配置");
    QVBoxLayout* networkLayout = new QVBoxLayout(networkGroup);

    // 网络模式选择
    m_networkModeGroup = new QButtonGroup(this);

    // 使用垂直布局来节省空间
    QVBoxLayout* modeLayout = new QVBoxLayout();
    m_lanModeRadio = new QRadioButton("小型局域网[同网段]");
    m_lanModeRadio->setChecked(true);
    m_networkModeGroup->addButton(m_lanModeRadio, 0);
    modeLayout->addWidget(m_lanModeRadio);

    m_wanModeRadio = new QRadioButton("中大型局域网[跨网段]或Internet互联网");
    m_networkModeGroup->addButton(m_wanModeRadio, 1);
    modeLayout->addWidget(m_wanModeRadio);

    networkLayout->addLayout(modeLayout);

    // IP和端口配置 - 放在网络模式内
    m_wanConfigGroup = new QGroupBox();
    m_wanConfigGroup->setEnabled(false);
    QFormLayout* wanLayout = new QFormLayout(m_wanConfigGroup);
    wanLayout->setLabelAlignment(Qt::AlignRight);

    // IP地址
    m_ipAddressEdit = new QLineEdit();
    m_ipAddressEdit->setPlaceholderText("*************");
    m_ipAddressEdit->setFixedWidth(150);
    m_ipAddressEdit->setObjectName("ipAddressEdit");
    wanLayout->addRow("IP地址:", m_ipAddressEdit);

    // 端口号
    m_portSpinBox = new QSpinBox();
    m_portSpinBox->setRange(1, 65535);
    m_portSpinBox->setValue(60000);
    m_portSpinBox->setFixedWidth(80);
    m_portSpinBox->setObjectName("portSpinBox");
    wanLayout->addRow("端口号:", m_portSpinBox);

    networkLayout->addWidget(m_wanConfigGroup);

    parentLayout->addRow(networkGroup);
}

void ControllerDialog::initializeDoorConfig(QVBoxLayout* parentLayout)
{
    // 门配置组
    QGroupBox* doorGroup = new QGroupBox("门配置");
    QVBoxLayout* doorLayout = new QVBoxLayout(doorGroup);
    doorLayout->setSpacing(8);

    // 创建4个门的配置界面，使用更紧凑的布局
    for (int i = 0; i < 4; ++i) {
        QGroupBox* singleDoorGroup = new QGroupBox(QString("门%1").arg(i + 1));
        singleDoorGroup->setMaximumHeight(150); // 限制高度
        QVBoxLayout* singleDoorLayout = new QVBoxLayout(singleDoorGroup);
        singleDoorLayout->setSpacing(5);

        // 门名称与状态同一行
        QHBoxLayout* nameStatusLayout = new QHBoxLayout();
        QLineEdit* nameEdit = new QLineEdit();
        nameEdit->setText(QString("门%1").arg(i + 1));
        nameEdit->setFixedWidth(80);
        nameEdit->setObjectName(QString("doorNameEdit_%1").arg(i));
        m_doorNameEdits.append(nameEdit);

        QCheckBox* enabledCheckBox = new QCheckBox("启用");
        enabledCheckBox->setChecked(i == 0); // 默认只启用第一个门
        enabledCheckBox->setObjectName(QString("doorEnabledCheckBox_%1").arg(i));
        m_doorEnabledCheckBoxes.append(enabledCheckBox);

        nameStatusLayout->addWidget(new QLabel("名称:"));
        nameStatusLayout->addWidget(nameEdit);
        nameStatusLayout->addWidget(enabledCheckBox);
        nameStatusLayout->addStretch();
        singleDoorLayout->addLayout(nameStatusLayout);

        // 控制方式与开门延时同一行
        QHBoxLayout* controlDelayLayout = new QHBoxLayout();
        QComboBox* controlModeCombo = new QComboBox();
        controlModeCombo->addItem("在线", static_cast<int>(Controller::DoorControlMode::Online));
        controlModeCombo->addItem("常开", static_cast<int>(Controller::DoorControlMode::AlwaysOpen));
        controlModeCombo->addItem("常闭", static_cast<int>(Controller::DoorControlMode::AlwaysClosed));
        controlModeCombo->setCurrentIndex(0); // 默认在线
        controlModeCombo->setFixedWidth(60);
        controlModeCombo->setObjectName(QString("doorControlModeCombo_%1").arg(i));
        m_doorControlModeComboBoxes.append(controlModeCombo);

        QSpinBox* openDelaySpinBox = new QSpinBox();
        openDelaySpinBox->setRange(0, 6000);
        openDelaySpinBox->setValue(3);
        openDelaySpinBox->setSuffix("秒");
        openDelaySpinBox->setFixedWidth(60);
        openDelaySpinBox->setObjectName(QString("doorOpenDelaySpinBox_%1").arg(i));
        m_doorOpenDelaySpinBoxes.append(openDelaySpinBox);

        controlDelayLayout->addWidget(new QLabel("控制:"));
        controlDelayLayout->addWidget(controlModeCombo);
        controlDelayLayout->addWidget(new QLabel("延时:"));
        controlDelayLayout->addWidget(openDelaySpinBox);
        controlDelayLayout->addStretch();
        singleDoorLayout->addLayout(controlDelayLayout);

        // 读卡器配置 - 分两行显示
        QHBoxLayout* entryReaderLayout = new QHBoxLayout();
        QCheckBox* entryEnabledCheckBox = new QCheckBox("进门读卡器");
        entryEnabledCheckBox->setChecked(true);
        entryEnabledCheckBox->setObjectName(QString("entryReaderEnabledCheckBox_%1").arg(i));
        m_entryReaderEnabledCheckBoxes.append(entryEnabledCheckBox);

        QCheckBox* entryAttendanceCheckBox = new QCheckBox("作考勤");
        entryAttendanceCheckBox->setObjectName(QString("entryAttendanceCheckBox_%1").arg(i));
        m_entryAttendanceCheckBoxes.append(entryAttendanceCheckBox);

        entryReaderLayout->addWidget(entryEnabledCheckBox);
        entryReaderLayout->addWidget(entryAttendanceCheckBox);
        entryReaderLayout->addStretch();
        singleDoorLayout->addLayout(entryReaderLayout);

        QHBoxLayout* exitReaderLayout = new QHBoxLayout();
        QCheckBox* exitEnabledCheckBox = new QCheckBox("出门读卡器");
        exitEnabledCheckBox->setChecked(true);
        exitEnabledCheckBox->setObjectName(QString("exitReaderEnabledCheckBox_%1").arg(i));
        m_exitReaderEnabledCheckBoxes.append(exitEnabledCheckBox);

        QCheckBox* exitAttendanceCheckBox = new QCheckBox("作考勤");
        exitAttendanceCheckBox->setObjectName(QString("exitAttendanceCheckBox_%1").arg(i));
        m_exitAttendanceCheckBoxes.append(exitAttendanceCheckBox);

        exitReaderLayout->addWidget(exitEnabledCheckBox);
        exitReaderLayout->addWidget(exitAttendanceCheckBox);
        exitReaderLayout->addStretch();
        singleDoorLayout->addLayout(exitReaderLayout);

        // 添加到门列表
        m_doorTabs.append(singleDoorGroup);
        doorLayout->addWidget(singleDoorGroup);
    }

    parentLayout->addWidget(doorGroup);
    parentLayout->addStretch(); // 添加弹性空间
}





void ControllerDialog::initializeButtonBox()
{
    m_buttonLayout = new QHBoxLayout();
    m_buttonLayout->addStretch();

    m_saveButton = new QPushButton("保存");
    m_saveButton->setObjectName("saveButton");
    m_saveButton->setMinimumWidth(80);
    m_buttonLayout->addWidget(m_saveButton);

    m_cancelButton = new QPushButton("取消");
    m_cancelButton->setObjectName("cancelButton");
    m_cancelButton->setMinimumWidth(80);
    m_buttonLayout->addWidget(m_cancelButton);
}

void ControllerDialog::initializeConnections()
{
    // 基本信息连接
    connect(m_controllerNumberSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &ControllerDialog::onControllerNumberChanged);
    connect(m_serialNumberEdit, &QLineEdit::textChanged, [this]() {
        m_validationTimer->start(); // 延迟验证
    });
    connect(m_areaComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &ControllerDialog::onAreaChanged);

    // 网络配置连接
    connect(m_networkModeGroup, QOverload<QAbstractButton*>::of(&QButtonGroup::buttonClicked),
            this, [this](QAbstractButton*) { onNetworkModeChanged(); });

    // 按钮连接
    connect(m_saveButton, &QPushButton::clicked, this, &ControllerDialog::onSaveClicked);
    connect(m_cancelButton, &QPushButton::clicked, this, &ControllerDialog::onCancelClicked);

    // 描述文字长度限制
    connect(m_descriptionEdit, &QTextEdit::textChanged, [this]() {
        QString text = m_descriptionEdit->toPlainText();
        if (text.length() > 60) {
            text = text.left(60);
            m_descriptionEdit->setPlainText(text);
            QTextCursor cursor = m_descriptionEdit->textCursor();
            cursor.movePosition(QTextCursor::End);
            m_descriptionEdit->setTextCursor(cursor);
        }
    });
}

void ControllerDialog::initializeStyles()
{
    setStyleSheet(R"(
        QDialog {
            background-color: #f5f5f5;
        }

        QTabWidget::pane {
            border: 1px solid #c0c0c0;
            background-color: white;
        }

        QTabBar::tab {
            background-color: #e0e0e0;
            padding: 8px 16px;
            margin-right: 2px;
        }

        QTabBar::tab:selected {
            background-color: white;
            border-bottom: 2px solid #007acc;
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #c0c0c0;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }

        QPushButton#saveButton {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }

        QPushButton#saveButton:hover {
            background-color: #005a9e;
        }

        QPushButton#cancelButton {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
        }

        QPushButton#cancelButton:hover {
            background-color: #545b62;
        }

        QLineEdit, QSpinBox, QComboBox, QTextEdit {
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }

        QLineEdit:focus, QSpinBox:focus, QComboBox:focus, QTextEdit:focus {
            border-color: #007acc;
        }
    )");
}

void ControllerDialog::onSerialNumberChanged()
{
    updateSerialNumberInfo();
    updateDoorConfigUI();
}

void ControllerDialog::onNetworkModeChanged()
{
    updateNetworkConfigUI();
}

void ControllerDialog::onControllerNumberChanged()
{
    // 检查控制器编号是否已存在
    int number = m_controllerNumberSpinBox->value();
    int excludeId = (m_mode == Mode::Edit) ? m_controllerId : -1;

    if (m_controllerDao->isControllerNumberExists(number, excludeId)) {
        m_controllerNumberSpinBox->setStyleSheet("border: 2px solid red;");
        m_controllerNumberSpinBox->setToolTip("控制器编号已存在");
    } else {
        m_controllerNumberSpinBox->setStyleSheet("");
        m_controllerNumberSpinBox->setToolTip("");
    }
}

void ControllerDialog::onAreaChanged()
{
    // 区域选择变化处理（如果需要的话）
}

void ControllerDialog::onSaveClicked()
{
    // 验证表单
    auto validation = validateForm();
    if (!validation.first) {
        QMessageBox::warning(this, "输入错误", validation.second);
        return;
    }

    // 获取表单数据
    Controller controller = getControllerFromForm();

    bool success = false;
    if (m_mode == Mode::Add) {
        // 添加控制器
        int controllerId = m_controllerDao->createController(controller);
        success = (controllerId > 0);
        if (success) {
            m_controllerId = controllerId;
            QMessageBox::information(this, "添加成功",
                QString("控制器 %1 添加成功！").arg(controller.controllerNumber()));
        }
    } else {
        // 更新控制器
        controller.setId(m_controllerId);
        controller.setCreatedAt(m_currentController.createdAt());
        success = m_controllerDao->updateController(controller);
        if (success) {
            QMessageBox::information(this, "更新成功",
                QString("控制器 %1 更新成功！").arg(controller.controllerNumber()));
        }
    }

    if (success) {
        accept();
    } else {
        QMessageBox::critical(this, "操作失败",
            m_mode == Mode::Add ? "添加控制器失败！" : "更新控制器失败！");
    }
}

void ControllerDialog::onCancelClicked()
{
    reject();
}

void ControllerDialog::loadControllerData()
{
    if (m_controllerId <= 0) {
        return;
    }

    m_currentController = m_controllerDao->findById(m_controllerId);
    if (m_currentController.id() <= 0) {
        QMessageBox::critical(this, "错误", "无法加载控制器数据！");
        reject();
        return;
    }

    setControllerToForm(m_currentController);
}

void ControllerDialog::loadAreaData()
{
    m_areaComboBox->clear();
    m_areaComboBox->addItem("无区域", 0);

    if (!m_areaDao) {
        return;
    }

    QList<Area> areas = m_areaDao->findAll();
    for (const Area& area : areas) {
        QString displayText = area.fullPath().isEmpty() ? area.name() : area.fullPath();
        m_areaComboBox->addItem(displayText, area.id());
    }
}

Controller ControllerDialog::getControllerFromForm()
{
    Controller controller;

    // 基本信息
    controller.setControllerNumber(m_controllerNumberSpinBox->value());
    controller.setSerialNumber(m_serialNumberEdit->text().trimmed());
    controller.setEnabled(m_enabledCheckBox->isChecked());

    // 区域
    int areaId = m_areaComboBox->currentData().toInt();
    controller.setAreaId(areaId);

    // 描述
    controller.setDescription(m_descriptionEdit->toPlainText().trimmed());

    // 扩展功能
    controller.setMobileRemoteEnabled(m_mobileRemoteCheckBox->isChecked());
    controller.setQrCodeEnabled(m_qrCodeCheckBox->isChecked());

    // 网络配置
    if (m_lanModeRadio->isChecked()) {
        controller.setNetworkMode(Controller::NetworkMode::LAN);
        controller.setIpAddress("");
        controller.setPort(60000);
    } else {
        controller.setNetworkMode(Controller::NetworkMode::WAN);
        controller.setIpAddress(m_ipAddressEdit->text().trimmed());
        controller.setPort(m_portSpinBox->value());
    }

    // 门配置
    QList<Controller::DoorConfig> doorConfigs;
    for (int i = 0; i < 4; ++i) {
        Controller::DoorConfig config;
        config.name = m_doorNameEdits[i]->text().trimmed();
        config.enabled = m_doorEnabledCheckBoxes[i]->isChecked();
        config.controlMode = static_cast<Controller::DoorControlMode>(
            m_doorControlModeComboBoxes[i]->currentData().toInt());
        config.openDelay = m_doorOpenDelaySpinBoxes[i]->value();
        config.entryReaderEnabled = m_entryReaderEnabledCheckBoxes[i]->isChecked();
        config.exitReaderEnabled = m_exitReaderEnabledCheckBoxes[i]->isChecked();
        config.entryAttendance = m_entryAttendanceCheckBoxes[i]->isChecked();
        config.exitAttendance = m_exitAttendanceCheckBoxes[i]->isChecked();

        doorConfigs.append(config);
    }
    controller.setAllDoorConfigs(doorConfigs);

    return controller;
}

void ControllerDialog::setControllerToForm(const Controller& controller)
{
    // 基本信息
    m_controllerNumberSpinBox->setValue(controller.controllerNumber());
    m_serialNumberEdit->setText(controller.serialNumber());
    m_enabledCheckBox->setChecked(controller.enabled());
    m_descriptionEdit->setPlainText(controller.description());
    m_mobileRemoteCheckBox->setChecked(controller.mobileRemoteEnabled());
    m_qrCodeCheckBox->setChecked(controller.qrCodeEnabled());

    // 区域选择
    for (int i = 0; i < m_areaComboBox->count(); ++i) {
        if (m_areaComboBox->itemData(i).toInt() == controller.areaId()) {
            m_areaComboBox->setCurrentIndex(i);
            break;
        }
    }

    // 网络配置
    if (controller.networkMode() == Controller::NetworkMode::LAN) {
        m_lanModeRadio->setChecked(true);
    } else {
        m_wanModeRadio->setChecked(true);
        m_ipAddressEdit->setText(controller.ipAddress());
        m_portSpinBox->setValue(controller.port());
    }
    onNetworkModeChanged();

    // 门配置
    QList<Controller::DoorConfig> doorConfigs = controller.allDoorConfigs();
    for (int i = 0; i < qMin(4, doorConfigs.size()); ++i) {
        const Controller::DoorConfig& config = doorConfigs[i];
        m_doorNameEdits[i]->setText(config.name);
        m_doorEnabledCheckBoxes[i]->setChecked(config.enabled);

        // 设置控制方式
        for (int j = 0; j < m_doorControlModeComboBoxes[i]->count(); ++j) {
            if (m_doorControlModeComboBoxes[i]->itemData(j).toInt() == static_cast<int>(config.controlMode)) {
                m_doorControlModeComboBoxes[i]->setCurrentIndex(j);
                break;
            }
        }

        m_doorOpenDelaySpinBoxes[i]->setValue(config.openDelay);
        m_entryReaderEnabledCheckBoxes[i]->setChecked(config.entryReaderEnabled);
        m_exitReaderEnabledCheckBoxes[i]->setChecked(config.exitReaderEnabled);
        m_entryAttendanceCheckBoxes[i]->setChecked(config.entryAttendance);
        m_exitAttendanceCheckBoxes[i]->setChecked(config.exitAttendance);
    }

    // 更新UI状态
    updateSerialNumberInfo();
    updateDoorConfigUI();
}

void ControllerDialog::updateDoorConfigUI()
{
    QString serialNumber = m_serialNumberEdit->text().trimmed();
    Controller tempController;
    tempController.setSerialNumber(serialNumber);

    int controllerType = tempController.getControllerType();
    bool isBidirectional = tempController.isBidirectionalController();

    // 根据控制器类型启用/禁用门组件
    for (int i = 0; i < 4; ++i) {
        bool doorAvailable = (i < controllerType);
        m_doorTabs[i]->setEnabled(doorAvailable);

        if (doorAvailable) {
            // 根据控制器类型设置出门读卡器的可用性
            bool exitReaderAvailable = isBidirectional;
            m_exitReaderEnabledCheckBoxes[i]->setEnabled(exitReaderAvailable);
            m_exitAttendanceCheckBoxes[i]->setEnabled(exitReaderAvailable);

            if (!exitReaderAvailable) {
                m_exitReaderEnabledCheckBoxes[i]->setChecked(false);
                m_exitAttendanceCheckBoxes[i]->setChecked(false);
            }
        } else {
            // 禁用的门，清空配置
            m_doorEnabledCheckBoxes[i]->setChecked(false);
        }
    }
}

void ControllerDialog::updateSerialNumberInfo()
{
    QString serialNumber = m_serialNumberEdit->text().trimmed();
    Controller tempController;
    tempController.setSerialNumber(serialNumber);

    // 更新控制器类型显示
    int type = tempController.getControllerType();
    m_controllerTypeLabel->setText(getControllerTypeText(type));

    // 更新系列名称显示
    QString seriesName = tempController.getSeriesName();
    m_seriesNameLabel->setText(seriesName);

    // 验证序列号格式
    if (!serialNumber.isEmpty()) {
        if (tempController.isValidSerialNumber()) {
            m_serialNumberEdit->setStyleSheet("");
            m_serialNumberEdit->setToolTip("");
        } else {
            m_serialNumberEdit->setStyleSheet("border: 2px solid red;");
            m_serialNumberEdit->setToolTip("序列号格式不正确");
        }

        // 检查序列号是否已存在
        int excludeId = (m_mode == Mode::Edit) ? m_controllerId : -1;
        if (m_controllerDao->isSerialNumberExists(serialNumber, excludeId)) {
            m_serialNumberEdit->setStyleSheet("border: 2px solid red;");
            m_serialNumberEdit->setToolTip("序列号已存在");
        }
    }
}

void ControllerDialog::updateNetworkConfigUI()
{
    bool isWanMode = m_wanModeRadio->isChecked();
    m_wanConfigGroup->setEnabled(isWanMode);

    if (!isWanMode) {
        m_ipAddressEdit->clear();
        m_portSpinBox->setValue(60000);
    }
}

QPair<bool, QString> ControllerDialog::validateForm()
{
    // 验证基本信息
    if (!validateBasicInfo()) {
        return qMakePair(false, "基本信息验证失败");
    }

    // 验证网络配置
    if (!validateNetworkConfig()) {
        return qMakePair(false, "网络配置验证失败");
    }

    // 验证门配置
    if (!validateDoorConfig()) {
        return qMakePair(false, "门配置验证失败");
    }

    return qMakePair(true, "");
}

bool ControllerDialog::validateBasicInfo()
{
    // 验证控制器编号
    int number = m_controllerNumberSpinBox->value();
    if (number <= 0) {
        QMessageBox::warning(this, "输入错误", "控制器编号必须大于0");
        m_controllerNumberSpinBox->setFocus();
        return false;
    }

    int excludeId = (m_mode == Mode::Edit) ? m_controllerId : -1;
    if (m_controllerDao->isControllerNumberExists(number, excludeId)) {
        QMessageBox::warning(this, "输入错误", QString("控制器编号 %1 已存在").arg(number));
        m_controllerNumberSpinBox->setFocus();
        return false;
    }

    // 验证序列号
    QString serialNumber = m_serialNumberEdit->text().trimmed();
    if (serialNumber.isEmpty()) {
        QMessageBox::warning(this, "输入错误", "序列号不能为空");
        m_serialNumberEdit->setFocus();
        return false;
    }

    Controller tempController;
    tempController.setSerialNumber(serialNumber);
    if (!tempController.isValidSerialNumber()) {
        QMessageBox::warning(this, "输入错误", "序列号格式不正确，必须为9位数字且符合规则");
        m_serialNumberEdit->setFocus();
        return false;
    }

    if (m_controllerDao->isSerialNumberExists(serialNumber, excludeId)) {
        QMessageBox::warning(this, "输入错误", QString("序列号 %1 已存在").arg(serialNumber));
        m_serialNumberEdit->setFocus();
        return false;
    }

    // 验证描述长度
    QString description = m_descriptionEdit->toPlainText().trimmed();
    if (description.length() > 60) {
        QMessageBox::warning(this, "输入错误", "说明文字不能超过60个字符");
        m_descriptionEdit->setFocus();
        return false;
    }

    return true;
}

bool ControllerDialog::validateNetworkConfig()
{
    if (m_wanModeRadio->isChecked()) {
        // 验证IP地址
        QString ipAddress = m_ipAddressEdit->text().trimmed();
        if (ipAddress.isEmpty()) {
            QMessageBox::warning(this, "输入错误", "跨网段模式下IP地址不能为空");
            m_ipAddressEdit->setFocus();
            return false;
        }

        Controller tempController;
        tempController.setNetworkMode(Controller::NetworkMode::WAN);
        tempController.setIpAddress(ipAddress);
        if (!tempController.isValidIpAddress()) {
            QMessageBox::warning(this, "输入错误", "IP地址格式不正确");
            m_ipAddressEdit->setFocus();
            return false;
        }

        // 验证端口号
        int port = m_portSpinBox->value();
        if (port <= 0 || port > 65535) {
            QMessageBox::warning(this, "输入错误", "端口号必须在1-65535范围内");
            m_portSpinBox->setFocus();
            return false;
        }
    }

    return true;
}

bool ControllerDialog::validateDoorConfig()
{
    QString serialNumber = m_serialNumberEdit->text().trimmed();
    Controller tempController;
    tempController.setSerialNumber(serialNumber);
    int maxDoors = tempController.getMaxDoors();

    // 检查是否至少有一个门启用
    bool hasEnabledDoor = false;
    for (int i = 0; i < maxDoors; ++i) {
        if (m_doorEnabledCheckBoxes[i]->isChecked()) {
            hasEnabledDoor = true;

            // 验证门名称
            QString doorName = m_doorNameEdits[i]->text().trimmed();
            if (doorName.isEmpty()) {
                QMessageBox::warning(this, "输入错误", QString("门%1的名称不能为空").arg(i + 1));
                m_doorNameEdits[i]->setFocus();
                return false;
            }
        }
    }

    if (!hasEnabledDoor) {
        QMessageBox::warning(this, "输入错误", "至少需要启用一个门");
        return false;
    }

    return true;
}

QString ControllerDialog::getControllerTypeText(int type)
{
    switch (type) {
        case 1: return "单门控制器";
        case 2: return "双门控制器";
        case 4: return "四门控制器";
        default: return "未知类型";
    }
}

QString ControllerDialog::getSeriesNameText(const QString& serialNumber)
{
    Controller tempController;
    tempController.setSerialNumber(serialNumber);
    return tempController.getSeriesName();
}

} // namespace AccessControl
