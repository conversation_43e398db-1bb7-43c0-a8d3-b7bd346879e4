#include <QApplication>
#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QGroupBox>
#include <QLabel>
#include <QLineEdit>
#include <QSpinBox>
#include <QComboBox>
#include <QCheckBox>
#include <QRadioButton>
#include <QButtonGroup>
#include <QTextEdit>
#include <QPushButton>

class TwoColumnControllerDialog : public QDialog
{
    Q_OBJECT

public:
    TwoColumnControllerDialog(QWidget *parent = nullptr) : QDialog(parent)
    {
        setWindowTitle("控制器添加对话框 - 两列布局调整");
        setModal(false);
        resize(950, 650); // 调整宽度和高度

        initializeUI();
    }

private:
    void initializeUI()
    {
        QVBoxLayout* mainLayout = new QVBoxLayout(this);
        mainLayout->setContentsMargins(10, 10, 10, 10);
        mainLayout->setSpacing(10);

        // 使用水平布局来创建两列
        QHBoxLayout* mainHorizontalLayout = new QHBoxLayout();
        mainHorizontalLayout->setSpacing(20);

        // 左列 - 基本信息和网络配置
        QWidget* leftColumn = new QWidget();
        QFormLayout* leftLayout = new QFormLayout(leftColumn);
        leftLayout->setLabelAlignment(Qt::AlignRight);
        leftLayout->setFormAlignment(Qt::AlignLeft | Qt::AlignTop);
        leftLayout->setVerticalSpacing(10);

        // 控制器编号
        QSpinBox* controllerNumberSpinBox = new QSpinBox();
        controllerNumberSpinBox->setRange(1, 9999);
        controllerNumberSpinBox->setValue(1);
        leftLayout->addRow("控制器编号*:", controllerNumberSpinBox);

        // 序列号SN - 自适应宽度
        QLineEdit* serialNumberEdit = new QLineEdit();
        serialNumberEdit->setMaxLength(9);
        serialNumberEdit->setPlaceholderText("请输入9位数字序列号");
        leftLayout->addRow("序列号SN*:", serialNumberEdit);

        // 类型和系列信息 - 放在序列号下面
        QHBoxLayout* typeSeriesLayout = new QHBoxLayout();
        QLabel* controllerTypeLabel = new QLabel("双门控制器");
        controllerTypeLabel->setStyleSheet("color: #666; font-style: italic;");
        QLabel* seriesNameLabel = new QLabel("中性系列");
        seriesNameLabel->setStyleSheet("color: #666; font-style: italic;");

        typeSeriesLayout->addWidget(new QLabel("类型:"));
        typeSeriesLayout->addWidget(controllerTypeLabel);
        typeSeriesLayout->addWidget(new QLabel("系列:"));
        typeSeriesLayout->addWidget(seriesNameLabel);
        typeSeriesLayout->addStretch(); // 添加弹性空间
        leftLayout->addRow("", typeSeriesLayout);

        // 启用状态
        QCheckBox* enabledCheckBox = new QCheckBox("启用控制器");
        enabledCheckBox->setChecked(true);
        leftLayout->addRow("状态:", enabledCheckBox);

        // 所在区域
        QComboBox* areaComboBox = new QComboBox();
        areaComboBox->addItem("无区域");
        areaComboBox->addItem("一楼");
        areaComboBox->addItem("二楼");
        leftLayout->addRow("所在区域:", areaComboBox);

        // 说明
        QTextEdit* descriptionEdit = new QTextEdit();
        descriptionEdit->setMaximumHeight(60);
        descriptionEdit->setPlaceholderText("请输入说明信息（最多60个字符）");
        leftLayout->addRow("说明:", descriptionEdit);

        // 网络配置
        QGroupBox* networkGroup = new QGroupBox("网络配置");
        QVBoxLayout* networkLayout = new QVBoxLayout(networkGroup);

        // 网络模式选择
        QButtonGroup* networkModeGroup = new QButtonGroup(this);

        QVBoxLayout* modeLayout = new QVBoxLayout();
        QRadioButton* lanModeRadio = new QRadioButton("小型局域网[同网段]");
        lanModeRadio->setChecked(true);
        networkModeGroup->addButton(lanModeRadio, 0);
        modeLayout->addWidget(lanModeRadio);

        QRadioButton* wanModeRadio = new QRadioButton("中大型局域网[跨网段]或Internet互联网");
        networkModeGroup->addButton(wanModeRadio, 1);
        modeLayout->addWidget(wanModeRadio);

        networkLayout->addLayout(modeLayout);

        // IP和端口配置
        QGroupBox* wanConfigGroup = new QGroupBox();
        wanConfigGroup->setEnabled(false);
        QFormLayout* wanLayout = new QFormLayout(wanConfigGroup);
        wanLayout->setLabelAlignment(Qt::AlignRight);

        QLineEdit* ipAddressEdit = new QLineEdit();
        ipAddressEdit->setPlaceholderText("*************");
        ipAddressEdit->setFixedWidth(150);
        wanLayout->addRow("IP地址:", ipAddressEdit);

        QSpinBox* portSpinBox = new QSpinBox();
        portSpinBox->setRange(1, 65535);
        portSpinBox->setValue(60000);
        portSpinBox->setFixedWidth(80);
        wanLayout->addRow("端口号:", portSpinBox);

        networkLayout->addWidget(wanConfigGroup);
        leftLayout->addRow(networkGroup);

        // 扩展功能 - 分两行显示，默认不开启
        QGroupBox* extendedGroup = new QGroupBox("扩展功能");
        QVBoxLayout* extendedLayout = new QVBoxLayout(extendedGroup);

        QCheckBox* mobileRemoteCheckBox = new QCheckBox("手机app远程开门");
        mobileRemoteCheckBox->setChecked(false); // 默认不开启
        extendedLayout->addWidget(mobileRemoteCheckBox);

        QCheckBox* qrCodeCheckBox = new QCheckBox("二维码开门功能");
        qrCodeCheckBox->setChecked(false); // 默认不开启
        extendedLayout->addWidget(qrCodeCheckBox);

        leftLayout->addRow(extendedGroup);

        // 右列 - 门配置
        QWidget* rightColumn = new QWidget();
        QVBoxLayout* rightLayout = new QVBoxLayout(rightColumn);
        rightLayout->setContentsMargins(0, 0, 0, 0);
        rightLayout->setSpacing(10);

        // 门配置
        QGroupBox* doorGroup = new QGroupBox("门配置");
        QVBoxLayout* doorLayout = new QVBoxLayout(doorGroup);
        doorLayout->setSpacing(8);

        // 创建2个门的配置界面作为示例
        for (int i = 0; i < 2; ++i) {
            QGroupBox* singleDoorGroup = new QGroupBox(QString("门%1").arg(i + 1));
            singleDoorGroup->setMaximumHeight(150);
            QVBoxLayout* singleDoorLayout = new QVBoxLayout(singleDoorGroup);
            singleDoorLayout->setSpacing(5);

            // 门名称与状态同一行
            QHBoxLayout* nameStatusLayout = new QHBoxLayout();
            QLineEdit* nameEdit = new QLineEdit();
            nameEdit->setText(QString("门%1").arg(i + 1));
            // 门名称自适应宽度，不设置固定宽度

            QCheckBox* enabledCheckBox = new QCheckBox("启用");
            enabledCheckBox->setChecked(i == 0);

            nameStatusLayout->addWidget(new QLabel("名称:"));
            nameStatusLayout->addWidget(nameEdit, 1); // 设置拉伸因子为1，自适应宽度
            nameStatusLayout->addWidget(enabledCheckBox);
            singleDoorLayout->addLayout(nameStatusLayout);

            // 控制方式、开门延时与门图标同一行
            QHBoxLayout* controlDelayLayout = new QHBoxLayout();
            QComboBox* controlModeCombo = new QComboBox();
            controlModeCombo->addItem("在线");
            controlModeCombo->addItem("常开");
            controlModeCombo->addItem("常闭");
            controlModeCombo->setCurrentIndex(0);
            controlModeCombo->setFixedWidth(60);

            QSpinBox* openDelaySpinBox = new QSpinBox();
            openDelaySpinBox->setRange(0, 6000);
            openDelaySpinBox->setValue(3);
            openDelaySpinBox->setSuffix("秒");
            openDelaySpinBox->setFixedWidth(60);

            // 门图标选择
            QComboBox* doorIconCombo = new QComboBox();
            doorIconCombo->addItem("门");
            doorIconCombo->addItem("人行通道闸");
            doorIconCombo->addItem("车辆通道闸");
            doorIconCombo->addItem("自动门");
            doorIconCombo->addItem("卷帘门");
            doorIconCombo->addItem("防火门");
            doorIconCombo->setCurrentIndex(0);
            doorIconCombo->setFixedWidth(100);

            controlDelayLayout->addWidget(new QLabel("控制:"));
            controlDelayLayout->addWidget(controlModeCombo);
            controlDelayLayout->addWidget(new QLabel("延时:"));
            controlDelayLayout->addWidget(openDelaySpinBox);
            controlDelayLayout->addWidget(new QLabel("图标:"));
            controlDelayLayout->addWidget(doorIconCombo);
            controlDelayLayout->addStretch();
            singleDoorLayout->addLayout(controlDelayLayout);

            // 读卡器配置 - 分两行显示
            QHBoxLayout* entryReaderLayout = new QHBoxLayout();
            QCheckBox* entryEnabledCheckBox = new QCheckBox("进门读卡器");
            entryEnabledCheckBox->setChecked(true);
            QCheckBox* entryAttendanceCheckBox = new QCheckBox("作考勤");

            entryReaderLayout->addWidget(entryEnabledCheckBox);
            entryReaderLayout->addWidget(entryAttendanceCheckBox);
            entryReaderLayout->addStretch();
            singleDoorLayout->addLayout(entryReaderLayout);

            QHBoxLayout* exitReaderLayout = new QHBoxLayout();
            QCheckBox* exitEnabledCheckBox = new QCheckBox("出门读卡器");
            exitEnabledCheckBox->setChecked(true);
            QCheckBox* exitAttendanceCheckBox = new QCheckBox("作考勤");

            exitReaderLayout->addWidget(exitEnabledCheckBox);
            exitReaderLayout->addWidget(exitAttendanceCheckBox);
            exitReaderLayout->addStretch();
            singleDoorLayout->addLayout(exitReaderLayout);

            doorLayout->addWidget(singleDoorGroup);
        }

        rightLayout->addWidget(doorGroup);
        rightLayout->addStretch();

        // 添加左右两列到主布局
        mainHorizontalLayout->addWidget(leftColumn, 1);
        mainHorizontalLayout->addWidget(rightColumn, 1);

        // 按钮区域
        QHBoxLayout* buttonLayout = new QHBoxLayout();
        buttonLayout->addStretch();

        QPushButton* saveButton = new QPushButton("保存");
        saveButton->setMinimumWidth(80);
        buttonLayout->addWidget(saveButton);

        QPushButton* cancelButton = new QPushButton("取消");
        cancelButton->setMinimumWidth(80);
        buttonLayout->addWidget(cancelButton);

        // 添加到主布局
        mainLayout->addLayout(mainHorizontalLayout, 1);
        mainLayout->addLayout(buttonLayout);

        // 连接信号
        connect(saveButton, &QPushButton::clicked, this, &QDialog::accept);
        connect(cancelButton, &QPushButton::clicked, this, &QDialog::reject);

        // 连接网络模式切换
        connect(wanModeRadio, &QRadioButton::toggled, [wanConfigGroup](bool checked) {
            wanConfigGroup->setEnabled(checked);
        });
    }
};

#include "test_two_column_layout.moc"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    TwoColumnControllerDialog dialog;
    dialog.show();

    return app.exec();
}
